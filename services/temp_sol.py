# Alternative: Ultra-Low Memory Approach Using Temporary Files
class UltraLowMemoryJSONCombiner:
    """
    For extremely large datasets, use temporary files for intermediate processing.
    """

    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="mcq_combine_")

    async def combine_with_temp_files(self, batch_json_files: List[str], resource_id: str,
                                      chapter_id: str, request_id: str,
                                      images_from_content: List[str] = None) -> Optional[str]:
        """
        Ultra-low memory approach using temporary files for processing.
        """
        temp_question_files = []

        try:
            # Step 1: Extract questions to individual temp files
            for i, batch_file in enumerate(batch_json_files):
                temp_file = os.path.join(self.temp_dir, f"questions_batch_{i}.jsonl")
                extracted_count = self._extract_questions_to_temp(batch_file, temp_file, request_id)

                if extracted_count > 0:
                    temp_question_files.append(temp_file)

                # Cleanup after each extraction
                gc.collect()

            # Step 2: Combine temp files into final output
            combined_json_filename = f"{chapter_id}_{resource_id}_mcqs.json"
            output_dir = os.path.dirname(batch_json_files[0])
            combined_json_path = os.path.join(output_dir, combined_json_filename)

            total_questions = self._combine_temp_files(temp_question_files, combined_json_path,
                                                       images_from_content, request_id)

            logger.info(f"[REQUEST:{request_id}] Ultra-low memory combination completed: {total_questions} questions")

            return combined_json_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in ultra-low memory combination: {e}")
            return None

        finally:
            # Clean up temp files
            self._cleanup_temp_files(temp_question_files)

    def _extract_questions_to_temp(self, input_file: str, output_file: str, request_id: str) -> int:
        """Extract questions from input file to temp file in JSONL format"""
        count = 0

        try:
            with open(input_file, "r", encoding="utf-8") as infile, \
                    open(output_file, "w", encoding="utf-8") as outfile:

                batch_json = json.load(infile)

                if "questions" in batch_json and isinstance(batch_json["questions"], list):
                    for question in batch_json["questions"]:
                        json.dump(question, outfile, ensure_ascii=False)
                        outfile.write('\n')
                        count += 1

                del batch_json

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error extracting questions: {e}")

        return count

    def _combine_temp_files(self, temp_files: List[str], output_file: str,
                            images_from_content: List[str], request_id: str) -> int:
        """Combine temp files into final JSON"""
        total_questions = 0

        with open(output_file, "w", encoding="utf-8") as outfile:
            outfile.write('{"questions": [\n')

            first_question = True
